# Frida蓝牙RSSI劫持工具

## 项目简介
基于Frida的Android蓝牙RSSI实时获取工具，专为小米14 Ultra Android 15设备优化。

## 功能特性
- 🎯 实时劫取系统蓝牙扫描结果
- 📊 获取设备名称、MAC地址、RSSI值
- 💾 JSON格式文件保存
- 🔔 Toast实时提示
- ⚡ 1秒间隔数据更新
- 🛡️ 智能去重和错误处理

## 系统要求
- Android 5.0+ (已测试Android 15)
- Root权限
- Frida环境
- 蓝牙和存储权限

## 快速开始
```bash
# 部署脚本
./scripts/deploy.sh

# 启动监控
./scripts/monitor.sh
```

## 输出格式
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "device_name": "Mi Band 7",
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "rssi": -45,
  "device_type": "BLE"
}
```

## 文件结构
- `src/` - 核心脚本文件
- `scripts/` - 部署和监控脚本
- `docs/` - 技术文档

## 作者
Frida蓝牙RSSI劫持工具 v1.0
