/**
 * 蓝牙RSSI劫持 - JSHook专用版本
 * 适配小米14 Ultra Android 15
 */

// 等待Java环境就绪
setTimeout(function() {
    console.log("🔧 开始初始化蓝牙RSSI Hook...");
    
    // 全局变量
    var deviceCache = {};
    var hookCount = 0;
    var writeCount = 0;
    var startTime = Date.now();
    
    // 配置
    var CONFIG = {
        RSSI_THRESHOLD: -100,
        FILE_PATH: "/sdcard/bluetooth_rssi_data.json",
        ENABLE_TOAST: true,
        FILTER_TIME: 3000,
        DEBUG: true
    };
    
    // 日志函数
    function log(message) {
        if (CONFIG.DEBUG) {
            console.log("[蓝牙RSSI] " + message);
        }
    }
    
    // 获取设备名称
    function getDeviceName(device) {
        try {
            var name = device.getName();
            if (name && name.length > 0) {
                return name;
            }
            return device.getAddress();
        } catch (e) {
            return "Unknown";
        }
    }
    
    // 检查是否应该记录
    function shouldRecord(macAddress, rssi) {
        // RSSI过滤
        if (rssi < CONFIG.RSSI_THRESHOLD || rssi > 0) {
            return false;
        }
        
        var now = Date.now();
        var key = macAddress;
        
        // 首次发现设备
        if (!deviceCache[key]) {
            deviceCache[key] = { time: now, rssi: rssi };
            return true;
        }
        
        // 检查时间和RSSI变化
        var cached = deviceCache[key];
        var timeDiff = now - cached.time;
        var rssiDiff = Math.abs(rssi - cached.rssi);
        
        if (timeDiff >= CONFIG.FILTER_TIME || rssiDiff >= 5) {
            deviceCache[key] = { time: now, rssi: rssi };
            return true;
        }
        
        return false;
    }
    
    // 写入文件
    function writeToFile(data) {
        try {
            var File = Java.use("java.io.File");
            var FileWriter = Java.use("java.io.FileWriter");
            var BufferedWriter = Java.use("java.io.BufferedWriter");
            
            var file = File.$new(CONFIG.FILE_PATH);
            var fileWriter = FileWriter.$new(file, true);
            var bufferedWriter = BufferedWriter.$new(fileWriter);
            
            var jsonString = JSON.stringify(data) + "\n";
            bufferedWriter.write(jsonString);
            bufferedWriter.flush();
            bufferedWriter.close();
            fileWriter.close();
            
            writeCount++;
            
            if (writeCount % 10 === 0) {
                log("已写入 " + writeCount + " 条记录");
            }
            
        } catch (e) {
            log("文件写入失败: " + e.message);
        }
    }
    
    // 显示Toast
    function showToast(message) {
        if (!CONFIG.ENABLE_TOAST) return;
        
        try {
            Java.scheduleOnMainThread(function() {
                try {
                    var Toast = Java.use("android.widget.Toast");
                    var ActivityThread = Java.use("android.app.ActivityThread");
                    var context = ActivityThread.currentApplication().getApplicationContext();
                    
                    var toast = Toast.makeText(context, message, Toast.LENGTH_SHORT.value);
                    toast.show();
                } catch (e) {
                    log("Toast显示失败: " + e.message);
                }
            });
        } catch (e) {
            log("Toast调度失败: " + e.message);
        }
    }
    
    // 处理扫描结果
    function handleScanResult(scanResult) {
        try {
            hookCount++;
            
            var device = scanResult.getDevice();
            var rssi = scanResult.getRssi();
            var macAddress = device.getAddress();
            var deviceName = getDeviceName(device);
            
            // 检查是否应该记录
            if (!shouldRecord(macAddress, rssi)) {
                return;
            }
            
            // 构造数据
            var data = {
                timestamp: new Date().toISOString(),
                device_name: deviceName,
                mac_address: macAddress,
                rssi: rssi,
                device_type: "BLE"
            };
            
            // 控制台输出
            log("[" + hookCount + "] " + deviceName + " | " + rssi + "dBm | " + macAddress);
            
            // 写入文件
            writeToFile(data);
            
            // 显示Toast
            var toastMessage = "🔵 " + deviceName + "\nRSSI: " + rssi + "dBm";
            showToast(toastMessage);
            
        } catch (e) {
            log("处理扫描结果失败: " + e.message);
        }
    }
    
    // 开始Hook
    Java.perform(function() {
        log("正在设置Hook...");
        
        try {
            // Hook ScanCallback.onScanResult
            var ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
            
            var originalOnScanResult = ScanCallback.onScanResult.overload('int', 'android.bluetooth.le.ScanResult');
            originalOnScanResult.implementation = function(callbackType, result) {
                // 调用原始方法
                var retval = this.onScanResult(callbackType, result);
                
                // 处理扫描结果
                handleScanResult(result);
                
                return retval;
            };
            
            log("✅ ScanCallback.onScanResult Hook成功");
            
        } catch (e) {
            log("❌ ScanCallback Hook失败: " + e.message);
        }
        
        try {
            // Hook BluetoothLeScanner.startScan (可选)
            var BluetoothLeScanner = Java.use("android.bluetooth.le.BluetoothLeScanner");
            
            var originalStartScan1 = BluetoothLeScanner.startScan.overload('android.bluetooth.le.ScanCallback');
            originalStartScan1.implementation = function(callback) {
                log("🔍 蓝牙扫描启动 - startScan(callback)");
                return this.startScan(callback);
            };
            
            var originalStartScan2 = BluetoothLeScanner.startScan.overload('java.util.List', 'android.bluetooth.le.ScanSettings', 'android.bluetooth.le.ScanCallback');
            originalStartScan2.implementation = function(filters, settings, callback) {
                log("🔍 蓝牙扫描启动 - startScan(filters, settings, callback)");
                return this.startScan(filters, settings, callback);
            };
            
            log("✅ BluetoothLeScanner Hook成功");
            
        } catch (e) {
            log("⚠️ BluetoothLeScanner Hook失败: " + e.message);
        }
        
        // 输出配置信息
        log("🚀 蓝牙RSSI监控已启动");
        log("📁 数据文件: " + CONFIG.FILE_PATH);
        log("📊 RSSI阈值: " + CONFIG.RSSI_THRESHOLD + "dBm");
        log("🔔 Toast显示: " + (CONFIG.ENABLE_TOAST ? "启用" : "禁用"));
        
        // 定期统计信息
        setInterval(function() {
            var runtime = Math.floor((Date.now() - startTime) / 1000);
            log("📊 运行时间: " + runtime + "s | Hook次数: " + hookCount + " | 写入记录: " + writeCount);
        }, 30000);
    });
    
}, 2000); // 延迟2秒启动

console.log("🔧 蓝牙RSSI劫持脚本已加载，等待初始化...");
