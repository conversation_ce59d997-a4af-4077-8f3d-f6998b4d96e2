/**
 * 蓝牙RSSI劫持脚本 - JSHook版本
 * 适配小米14 Ultra Android 15
 */

// 配置参数
var CONFIG = {
    RSSI_THRESHOLD: -100,        // RSSI阈值
    FILE_PATH: "/sdcard/bluetooth_rssi.json",
    ENABLE_TOAST: true,
    ENABLE_LOG: true,
    FILTER_TIME: 3000           // 去重时间(ms)
};

// 全局变量
var deviceCache = {};
var hookCount = 0;
var writeCount = 0;

// 日志函数
function log(msg) {
    if (CONFIG.ENABLE_LOG) {
        console.log("[蓝牙RSSI] " + msg);
    }
}

// 获取设备名称
function getDeviceName(device) {
    try {
        var name = device.getName();
        return name ? name : device.getAddress();
    } catch (e) {
        return "Unknown";
    }
}

// 检查是否应该记录
function shouldRecord(macAddress, rssi) {
    var now = Date.now();
    var key = macAddress;
    
    if (rssi < CONFIG.RSSI_THRESHOLD || rssi > 0) {
        return false;
    }
    
    if (!deviceCache[key]) {
        deviceCache[key] = { time: now, rssi: rssi };
        return true;
    }
    
    var cached = deviceCache[key];
    var timeDiff = now - cached.time;
    var rssiDiff = Math.abs(rssi - cached.rssi);
    
    if (timeDiff >= CONFIG.FILTER_TIME || rssiDiff >= 5) {
        deviceCache[key] = { time: now, rssi: rssi };
        return true;
    }
    
    return false;
}

// 写入文件
function writeToFile(data) {
    try {
        var File = Java.use("java.io.File");
        var FileWriter = Java.use("java.io.FileWriter");
        var BufferedWriter = Java.use("java.io.BufferedWriter");
        
        var file = File.$new(CONFIG.FILE_PATH);
        var fileWriter = FileWriter.$new(file, true);
        var bufferedWriter = BufferedWriter.$new(fileWriter);
        
        var jsonStr = JSON.stringify(data) + "\n";
        bufferedWriter.write(jsonStr);
        bufferedWriter.close();
        fileWriter.close();
        
        writeCount++;
        if (writeCount % 10 === 0) {
            log("已写入 " + writeCount + " 条记录");
        }
    } catch (e) {
        log("文件写入失败: " + e.message);
    }
}

// 显示Toast
function showToast(message) {
    if (!CONFIG.ENABLE_TOAST) return;
    
    try {
        Java.scheduleOnMainThread(function() {
            var Toast = Java.use("android.widget.Toast");
            var ActivityThread = Java.use("android.app.ActivityThread");
            var context = ActivityThread.currentApplication().getApplicationContext();
            
            var toast = Toast.makeText(context, message, Toast.LENGTH_SHORT.value);
            toast.show();
        });
    } catch (e) {
        log("Toast显示失败: " + e.message);
    }
}

// 处理扫描结果
function handleScanResult(scanResult) {
    try {
        hookCount++;
        
        var device = scanResult.getDevice();
        var rssi = scanResult.getRssi();
        var macAddress = device.getAddress();
        var deviceName = getDeviceName(device);
        
        if (!shouldRecord(macAddress, rssi)) {
            return;
        }
        
        var data = {
            timestamp: new Date().toISOString(),
            device_name: deviceName,
            mac_address: macAddress,
            rssi: rssi,
            device_type: "BLE"
        };
        
        // 控制台输出
        log("[" + hookCount + "] " + deviceName + " | " + rssi + "dBm | " + macAddress);
        
        // 写入文件
        writeToFile(data);
        
        // 显示Toast
        var toastMsg = "🔵 " + deviceName + "\nRSSI: " + rssi + "dBm";
        showToast(toastMsg);
        
    } catch (e) {
        log("处理扫描结果失败: " + e.message);
    }
}

// 主Hook函数
function startHook() {
    Java.perform(function() {
        log("开始Hook蓝牙扫描...");
        
        try {
            // Hook ScanCallback.onScanResult
            var ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
            
            ScanCallback.onScanResult.overload('int', 'android.bluetooth.le.ScanResult').implementation = function(callbackType, result) {
                // 调用原始方法
                this.onScanResult(callbackType, result);
                
                // 处理扫描结果
                handleScanResult(result);
            };
            
            log("✅ ScanCallback Hook成功");
            
        } catch (e) {
            log("❌ ScanCallback Hook失败: " + e.message);
        }
        
        try {
            // Hook BluetoothLeScanner.startScan
            var BluetoothLeScanner = Java.use("android.bluetooth.le.BluetoothLeScanner");
            
            BluetoothLeScanner.startScan.overload('android.bluetooth.le.ScanCallback').implementation = function(callback) {
                log("🔍 蓝牙扫描启动");
                return this.startScan(callback);
            };
            
            log("✅ BluetoothLeScanner Hook成功");
            
        } catch (e) {
            log("❌ BluetoothLeScanner Hook失败: " + e.message);
        }
        
        log("🚀 蓝牙RSSI监控已启动");
        log("📁 数据文件: " + CONFIG.FILE_PATH);
        log("📊 RSSI阈值: " + CONFIG.RSSI_THRESHOLD + "dBm");
        
        // 定期统计
        setInterval(function() {
            log("📊 Hook次数: " + hookCount + " | 写入记录: " + writeCount);
        }, 30000);
    });
}

// 启动Hook
setTimeout(startHook, 1000);

log("🔧 蓝牙RSSI劫持脚本已加载");
