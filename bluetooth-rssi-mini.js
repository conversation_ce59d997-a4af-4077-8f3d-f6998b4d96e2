// 蓝牙RSSI劫持 - 精简版
var cache = {};
var count = 0;

function log(msg) { console.log("[BT] " + msg); }

function writeFile(data) {
    try {
        var File = Java.use("java.io.File");
        var FileWriter = Java.use("java.io.FileWriter");
        var file = File.$new("/sdcard/bt_rssi.json");
        var writer = FileWriter.$new(file, true);
        writer.write(JSON.stringify(data) + "\n");
        writer.close();
        count++;
    } catch (e) { log("写入失败"); }
}

function showToast(msg) {
    Java.scheduleOnMainThread(function() {
        try {
            var Toast = Java.use("android.widget.Toast");
            var context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
            Toast.makeText(context, msg, 0).show();
        } catch (e) {}
    });
}

Java.perform(function() {
    try {
        var ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
        ScanCallback.onScanResult.overload('int', 'android.bluetooth.le.ScanResult').implementation = function(type, result) {
            this.onScanResult(type, result);
            
            try {
                var device = result.getDevice();
                var rssi = result.getRssi();
                var mac = device.getAddress();
                var name = device.getName() || mac;
                
                if (rssi < -100 || rssi > 0) return;
                
                var now = Date.now();
                if (cache[mac] && (now - cache[mac]) < 3000) return;
                cache[mac] = now;
                
                var data = {
                    time: new Date().toISOString(),
                    name: name,
                    mac: mac,
                    rssi: rssi
                };
                
                log(name + " | " + rssi + "dBm");
                writeFile(data);
                showToast("🔵 " + name + "\n" + rssi + "dBm");
                
            } catch (e) {}
        };
        
        log("✅ Hook成功，监控已启动");
        
    } catch (e) {
        log("❌ Hook失败: " + e.message);
    }
});
