// 蓝牙Hook测试脚本 - 超简版
console.log("🔧 测试脚本开始加载...");

setTimeout(function() {
    console.log("⏰ 开始初始化...");
    
    Java.perform(function() {
        console.log("☕ Java环境已就绪");
        
        try {
            // 简单的Hook测试
            var ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
            console.log("📱 找到ScanCallback类");
            
            ScanCallback.onScanResult.overload('int', 'android.bluetooth.le.ScanResult').implementation = function(type, result) {
                console.log("🎯 Hook成功！检测到蓝牙扫描结果");
                
                try {
                    var device = result.getDevice();
                    var rssi = result.getRssi();
                    var mac = device.getAddress();
                    var name = device.getName() || "Unknown";
                    
                    console.log("📡 设备: " + name + " | RSSI: " + rssi + "dBm | MAC: " + mac);
                    
                } catch (e) {
                    console.log("❌ 数据提取失败: " + e.message);
                }
                
                // 调用原始方法
                return this.onScanResult(type, result);
            };
            
            console.log("✅ Hook设置完成，等待蓝牙扫描...");
            
        } catch (e) {
            console.log("❌ Hook失败: " + e.message);
        }
    });
    
}, 1000);

console.log("📋 测试脚本加载完成");
