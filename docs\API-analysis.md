# Android蓝牙API分析文档

## 概述
本文档详细分析了Android蓝牙系统中用于RSSI获取的关键API，为Frida Hook提供技术参考。

## 核心API分析

### 1. BluetoothLeScanner
**包路径**: `android.bluetooth.le.BluetoothLeScanner`

#### 关键方法
```java
// 启动BLE扫描
public void startScan(ScanCallback callback)
public void startScan(List<ScanFilter> filters, ScanSettings settings, ScanCallback callback)

// 停止BLE扫描
public void stopScan(ScanCallback callback)
```

#### Hook策略
- **目标**: 监控扫描启动和停止
- **用途**: 了解扫描时机，确保Hook时机正确
- **注意**: 不直接提供RSSI数据

### 2. ScanCallback
**包路径**: `android.bluetooth.le.ScanCallback`

#### 关键方法
```java
// 扫描结果回调 - 主要Hook目标
public void onScanResult(int callbackType, ScanResult result)

// 批量扫描结果
public void onBatchScanResults(List<ScanResult> results)

// 扫描失败回调
public void onScanFailed(int errorCode)
```

#### Hook策略
- **主要目标**: `onScanResult(int, ScanResult)`
- **数据来源**: ScanResult对象包含完整的设备信息
- **实时性**: 每次发现设备时立即触发

### 3. ScanResult
**包路径**: `android.bluetooth.le.ScanResult`

#### 关键方法
```java
// 获取蓝牙设备对象
public BluetoothDevice getDevice()

// 获取RSSI值 - 核心数据
public int getRssi()

// 获取扫描记录
public ScanRecord getScanRecord()

// 获取时间戳
public long getTimestampNanos()
```

#### 数据提取
```javascript
// Frida Hook示例
const device = scanResult.getDevice();
const rssi = scanResult.getRssi();
const scanRecord = scanResult.getScanRecord();
const timestamp = scanResult.getTimestampNanos();
```

### 4. BluetoothDevice
**包路径**: `android.bluetooth.BluetoothDevice`

#### 关键方法
```java
// 获取设备名称
public String getName()

// 获取MAC地址
public String getAddress()

// 获取设备类型
public int getType()

// 获取蓝牙类
public BluetoothClass getBluetoothClass()
```

#### Hook策略
- **目标**: 获取设备友好名称
- **备选**: 如果getName()返回null，使用getAddress()

### 5. ScanRecord
**包路径**: `android.bluetooth.le.ScanRecord`

#### 关键方法
```java
// 获取原始广播数据
public byte[] getBytes()

// 获取设备名称（从广播数据中）
public String getDeviceName()

// 获取服务UUID列表
public List<ParcelUuid> getServiceUuids()

// 获取制造商数据
public SparseArray<byte[]> getManufacturerSpecificData()
```

## Android版本兼容性

### Android 5.0+ (API 21+)
- 使用新的BLE API: `BluetoothLeScanner`
- 支持`ScanCallback`和`ScanResult`
- **推荐使用**: 功能完整，性能优秀

### Android 4.3-4.4 (API 18-20)
- 使用旧的API: `BluetoothAdapter.startLeScan()`
- 回调接口: `BluetoothAdapter.LeScanCallback`
- **兼容性Hook**: 需要额外处理

```java
// 旧版API
public boolean startLeScan(LeScanCallback callback)
public boolean startLeScan(UUID[] serviceUuids, LeScanCallback callback)

// 回调接口
public interface LeScanCallback {
    void onLeScan(BluetoothDevice device, int rssi, byte[] scanRecord);
}
```

## 小米MIUI特殊处理

### MIUI蓝牙服务
- **进程名**: `com.miui.bluetooth`
- **特点**: 小米定制的蓝牙服务
- **Hook策略**: 并行Hook标准服务和MIUI服务

### MIUI权限管理
- **自启动管理**: 确保蓝牙服务自启动权限
- **后台运行**: 允许应用后台运行
- **悬浮窗权限**: Toast显示可能需要悬浮窗权限

## Hook实现策略

### 1. 进程选择
```javascript
// 优先级顺序
const targetProcesses = [
    "com.android.bluetooth",    // 标准蓝牙服务
    "com.miui.bluetooth",       // 小米定制服务
    "system_server"             // 系统服务（备选）
];
```

### 2. Hook时机
```javascript
// 等待Java环境初始化
Java.perform(function() {
    // 延迟Hook，确保类已加载
    setTimeout(function() {
        hookScanCallback();
    }, 1000);
});
```

### 3. 错误处理
```javascript
try {
    const ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
    // Hook实现
} catch (error) {
    console.log("Hook失败，尝试备选方案");
    // 降级到旧版API
}
```

## 数据流分析

### 正常扫描流程
1. 应用调用`BluetoothLeScanner.startScan()`
2. 系统开始BLE扫描
3. 发现设备时触发`ScanCallback.onScanResult()`
4. 从`ScanResult`提取设备信息和RSSI
5. 应用处理扫描结果

### Hook介入点
```
应用 → BluetoothLeScanner.startScan() → 系统蓝牙栈
                                           ↓
Hook ← ScanCallback.onScanResult() ← 扫描结果
  ↓
数据处理 → 文件写入 + Toast显示
```

## 性能考虑

### 1. Hook开销
- **CPU影响**: 每次回调增加约1-2ms处理时间
- **内存影响**: 缓存机制控制在50MB以内
- **I/O影响**: 批量写入减少磁盘操作

### 2. 优化策略
- **去重过滤**: 避免重复处理相同设备
- **异步处理**: 文件写入和Toast显示异步执行
- **内存管理**: 定期清理过期缓存

### 3. 资源限制
- **扫描频率**: 系统限制约1秒1次
- **设备数量**: 单次扫描通常发现5-20个设备
- **数据量**: 每小时约产生1-10MB数据

## 安全注意事项

### 1. 权限要求
- **位置权限**: Android 6.0+需要位置权限进行BLE扫描
- **蓝牙权限**: BLUETOOTH和BLUETOOTH_ADMIN权限
- **存储权限**: 写入外部存储权限

### 2. 隐私保护
- **MAC地址**: 可选择性记录或脱敏处理
- **设备名称**: 过滤敏感设备名称
- **数据加密**: 本地存储可选加密

### 3. 系统稳定性
- **Hook范围**: 仅Hook必要的方法
- **异常处理**: 完善的错误恢复机制
- **资源清理**: 及时释放不需要的资源

## 故障排除

### 常见问题
1. **Hook失败**: 检查目标进程是否存在
2. **无数据**: 确认蓝牙已开启且有设备在附近
3. **权限错误**: 检查root权限和应用权限
4. **文件写入失败**: 检查存储权限和磁盘空间

### 调试方法
1. **日志输出**: 使用console.log记录关键信息
2. **异常捕获**: try-catch包装所有Hook代码
3. **状态检查**: 定期检查Hook状态和数据流
4. **性能监控**: 监控CPU和内存使用情况
