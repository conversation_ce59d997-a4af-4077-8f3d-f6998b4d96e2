# 故障排除指南

## 常见问题及解决方案

### 1. 部署相关问题

#### 问题: "ADB未找到"
**症状**: 运行部署脚本时提示ADB命令不存在
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt install android-tools-adb

# macOS
brew install android-platform-tools

# Windows
# 下载Android SDK Platform Tools并添加到PATH
```

#### 问题: "未检测到Android设备"
**症状**: adb devices显示空或offline
**解决方案**:
1. 检查USB连接
2. 启用开发者选项和USB调试
3. 确认设备授权ADB连接
```bash
adb kill-server
adb start-server
adb devices
```

#### 问题: "设备未获得root权限"
**症状**: su命令执行失败
**解决方案**:
1. 确认设备已正确root
2. 检查Magisk或SuperSU是否正常工作
3. 授权ADB shell的root权限
```bash
adb shell
su  # 应该显示root提示符
```

### 2. Frida相关问题

#### 问题: "Frida未找到"
**症状**: frida命令不存在
**解决方案**:
```bash
# 安装Frida
pip install frida-tools

# 验证安装
frida --version
```

#### 问题: "Frida服务启动失败"
**症状**: 无法连接到frida-server
**解决方案**:
1. 下载对应架构的frida-server
2. 推送到设备并启动
```bash
# 下载frida-server (替换版本号)
wget https://github.com/frida/frida/releases/download/16.0.0/frida-server-16.0.0-android-arm64.xz

# 解压并推送
unxz frida-server-16.0.0-android-arm64.xz
adb push frida-server-16.0.0-android-arm64 /data/local/tmp/frida-server
adb shell "chmod 755 /data/local/tmp/frida-server"

# 启动服务
adb shell "su -c '/data/local/tmp/frida-server &'"
```

#### 问题: "进程附加失败"
**症状**: frida无法附加到蓝牙进程
**解决方案**:
1. 检查目标进程是否存在
```bash
adb shell "ps | grep bluetooth"
```
2. 尝试不同的进程名
3. 使用spawn模式而非attach模式

### 3. 蓝牙相关问题

#### 问题: "未找到蓝牙进程"
**症状**: 无法找到com.android.bluetooth进程
**解决方案**:
1. 确认蓝牙已开启
```bash
adb shell "settings put global bluetooth_on 1"
```
2. 重启蓝牙服务
```bash
adb shell "am force-stop com.android.bluetooth"
adb shell "am start -a android.bluetooth.adapter.action.REQUEST_ENABLE"
```
3. 检查小米定制服务
```bash
adb shell "ps | grep miui"
```

#### 问题: "Hook成功但无数据"
**症状**: Hook显示成功但没有扫描结果
**解决方案**:
1. 确认附近有蓝牙设备
2. 手动触发蓝牙扫描
```bash
adb shell "am start -a android.bluetooth.adapter.action.REQUEST_DISCOVERABLE"
```
3. 检查权限设置
4. 降低RSSI阈值

### 4. 数据输出问题

#### 问题: "文件写入失败"
**症状**: 无法创建或写入数据文件
**解决方案**:
1. 检查存储权限
```bash
adb shell "ls -la /sdcard/"
```
2. 检查磁盘空间
```bash
adb shell "df -h /sdcard"
```
3. 更改文件路径
```javascript
// 在config.js中修改
FILE_PATH: "/data/local/tmp/bluetooth_rssi_data.json"
```

#### 问题: "Toast不显示"
**症状**: 没有Toast提示出现
**解决方案**:
1. 检查Toast权限（MIUI）
2. 确认应用上下文获取成功
3. 手动测试Toast
```javascript
Java.scheduleOnMainThread(() => {
    const Toast = Java.use("android.widget.Toast");
    const context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
    Toast.makeText(context, "测试Toast", Toast.LENGTH_SHORT.value).show();
});
```

### 5. 性能问题

#### 问题: "CPU使用率过高"
**症状**: 设备发热，系统卡顿
**解决方案**:
1. 增加去重时间窗口
```javascript
DUPLICATE_FILTER_WINDOW: 5000  // 增加到5秒
```
2. 提高RSSI阈值
```javascript
RSSI_THRESHOLD: -80  // 只记录信号较强的设备
```
3. 减少批量写入频率

#### 问题: "内存使用过多"
**症状**: 应用内存持续增长
**解决方案**:
1. 减少缓存大小
```javascript
MAX_CACHE_SIZE: 50  // 减少到50个设备
```
2. 增加清理频率
```javascript
MEMORY_CLEANUP_INTERVAL: 30000  // 30秒清理一次
```

### 6. 小米设备特殊问题

#### 问题: "MIUI权限限制"
**症状**: 功能在其他设备正常，小米设备异常
**解决方案**:
1. 关闭MIUI优化
   - 设置 → 更多设置 → 开发者选项 → 关闭MIUI优化
2. 设置应用权限
   - 自启动管理：允许相关应用自启动
   - 后台应用管理：无限制
   - 悬浮窗权限：允许显示悬浮窗
3. 关闭省电模式

#### 问题: "小米蓝牙服务冲突"
**症状**: 标准蓝牙Hook正常，但数据不完整
**解决方案**:
1. 同时Hook小米蓝牙服务
```javascript
const targetProcesses = [
    "com.android.bluetooth",
    "com.miui.bluetooth"
];
```
2. 检查小米蓝牙服务状态
```bash
adb shell "dumpsys activity services | grep bluetooth"
```

### 7. 调试技巧

#### 启用详细日志
```javascript
// 在config.js中设置
DEBUG: true,
LOGGING: {
    LEVEL: "DEBUG"
}
```

#### 使用ADB日志
```bash
# 实时查看日志
adb logcat | grep -i frida

# 过滤蓝牙相关日志
adb logcat | grep -i bluetooth
```

#### 手动测试Hook
```javascript
// 简化的测试Hook
Java.perform(function() {
    const ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
    ScanCallback.onScanResult.implementation = function(callbackType, result) {
        console.log("Hook测试成功！");
        return this.onScanResult(callbackType, result);
    };
});
```

### 8. 错误代码参考

#### Frida错误代码
- `Failed to attach`: 进程不存在或权限不足
- `Script error`: JavaScript语法错误或API调用失败
- `Process crashed`: 目标进程崩溃

#### Android蓝牙错误代码
- `SCAN_FAILED_ALREADY_STARTED (1)`: 扫描已在进行
- `SCAN_FAILED_APPLICATION_REGISTRATION_FAILED (2)`: 应用注册失败
- `SCAN_FAILED_FEATURE_UNSUPPORTED (4)`: 设备不支持BLE

### 9. 恢复步骤

#### 完全重置
```bash
# 停止所有相关进程
pkill -f frida
adb shell "am force-stop com.android.bluetooth"

# 清理临时文件
adb shell "rm -rf /sdcard/frida-bluetooth"
adb shell "rm -f /sdcard/bluetooth_rssi_data.json"

# 重启蓝牙服务
adb shell "svc bluetooth disable"
adb shell "svc bluetooth enable"

# 重新部署
./scripts/deploy.sh
```

#### 备份重要数据
```bash
# 备份数据文件
adb pull /sdcard/bluetooth_rssi_data.json ./backup/

# 备份配置
cp src/config.js ./backup/config.js.backup
```

### 10. 联系支持

如果以上解决方案都无法解决问题，请提供以下信息：

1. **设备信息**:
   - 设备型号
   - Android版本
   - MIUI版本（如适用）
   - Root方法

2. **错误信息**:
   - 完整的错误日志
   - 复现步骤
   - 预期行为vs实际行为

3. **环境信息**:
   - Frida版本
   - ADB版本
   - 操作系统

4. **测试结果**:
   - 运行`./scripts/test.sh`的完整输出
   - 相关的adb logcat日志
