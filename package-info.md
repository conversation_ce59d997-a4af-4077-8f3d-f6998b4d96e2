# 文件导入指南

## 方案选择

### 🎯 推荐方案：单文件版本
**文件**: `bluetooth-rssi-hook-all-in-one.js`
- ✅ 只需导入一个文件
- ✅ 包含所有功能模块
- ✅ 配置集中管理
- ✅ 适合算法助手等工具

### 📁 完整项目版本
如果您的算法助手支持多文件导入，可以使用完整的项目结构：

```
必需文件：
├── src/bluetooth-rssi-hook.js     # 主脚本
├── src/config.js                  # 配置文件
└── src/utils/                     # 工具模块
    ├── data-processor.js
    ├── file-writer.js
    └── toast-helper.js

可选文件：
├── scripts/                       # 部署脚本
├── docs/                         # 文档
└── README.md                     # 说明
```

## 使用方法

### 单文件版本使用
```bash
# 直接使用单文件
frida -U -f com.android.bluetooth -l bluetooth-rssi-hook-all-in-one.js --no-pause

# 或者attach模式
frida -U -p <蓝牙进程PID> -l bluetooth-rssi-hook-all-in-one.js
```

### 配置修改
在单文件版本中，所有配置都在文件开头的CONFIG对象中：

```javascript
const CONFIG = {
    // 修改RSSI阈值
    RSSI_THRESHOLD: -80,
    
    // 修改文件路径
    OUTPUT: {
        FILE_PATH: "/sdcard/my_bluetooth_data.json",
        ENABLE_TOAST: false  // 禁用Toast
    }
};
```

## 算法助手导入建议

1. **如果支持单文件**: 使用 `bluetooth-rssi-hook-all-in-one.js`
2. **如果支持多文件**: 导入整个 `src/` 目录
3. **如果有文件大小限制**: 可以进一步精简代码

## 精简版本（如有需要）

如果您的算法助手对文件大小有严格限制，我可以创建一个更精简的版本，去除：
- 详细的错误处理
- 性能优化代码
- 调试日志
- Toast功能

请告诉我您的具体需求！
