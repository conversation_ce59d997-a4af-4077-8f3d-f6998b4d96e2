#!/bin/bash

# Frida蓝牙RSSI劫持工具部署脚本
# 适配小米14 Ultra Android 15

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEVICE_PATH="/sdcard/frida-bluetooth"
MAIN_SCRIPT="$PROJECT_ROOT/src/bluetooth-rssi-hook.js"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查adb
    if ! command -v adb &> /dev/null; then
        log_error "ADB未找到，请安装Android SDK"
        exit 1
    fi
    
    # 检查frida
    if ! command -v frida &> /dev/null; then
        log_error "Frida未找到，请安装Frida工具"
        exit 1
    fi
    
    # 检查设备连接
    if ! adb devices | grep -q "device$"; then
        log_error "未检测到Android设备，请检查USB连接和调试模式"
        exit 1
    fi
    
    # 检查root权限
    if ! adb shell "su -c 'id'" 2>/dev/null | grep -q "uid=0"; then
        log_error "设备未获得root权限或未授权"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 检查设备信息
check_device_info() {
    log_info "检查设备信息..."
    
    local device_model=$(adb shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    local android_version=$(adb shell getprop ro.build.version.release 2>/dev/null | tr -d '\r')
    local miui_version=$(adb shell getprop ro.miui.ui.version.name 2>/dev/null | tr -d '\r')
    
    log_info "设备型号: $device_model"
    log_info "Android版本: $android_version"
    log_info "MIUI版本: $miui_version"
    
    # 检查是否为小米设备
    if [[ "$device_model" == *"14 Ultra"* ]] || [[ "$miui_version" != "" ]]; then
        log_success "检测到小米设备，配置已优化"
    else
        log_warning "非小米设备，可能需要调整配置"
    fi
}

# 检查蓝牙服务
check_bluetooth_service() {
    log_info "检查蓝牙服务..."
    
    # 检查蓝牙进程
    local bt_processes=$(adb shell "ps | grep bluetooth" 2>/dev/null || true)
    
    if echo "$bt_processes" | grep -q "com.android.bluetooth"; then
        log_success "发现标准蓝牙服务: com.android.bluetooth"
    else
        log_warning "未发现标准蓝牙服务"
    fi
    
    if echo "$bt_processes" | grep -q "com.miui.bluetooth"; then
        log_success "发现小米蓝牙服务: com.miui.bluetooth"
    else
        log_info "未发现小米定制蓝牙服务"
    fi
    
    # 检查蓝牙状态
    local bt_enabled=$(adb shell "settings get global bluetooth_on" 2>/dev/null | tr -d '\r')
    if [[ "$bt_enabled" == "1" ]]; then
        log_success "蓝牙已启用"
    else
        log_warning "蓝牙未启用，建议手动开启"
    fi
}

# 创建设备目录
setup_device_directories() {
    log_info "设置设备目录..."
    
    adb shell "su -c 'mkdir -p $DEVICE_PATH'" 2>/dev/null || {
        log_error "无法创建设备目录"
        exit 1
    }
    
    adb shell "su -c 'chmod 755 $DEVICE_PATH'" 2>/dev/null
    log_success "设备目录创建完成: $DEVICE_PATH"
}

# 上传脚本文件
upload_scripts() {
    log_info "上传脚本文件..."
    
    # 上传主脚本
    if [[ -f "$MAIN_SCRIPT" ]]; then
        adb push "$MAIN_SCRIPT" "$DEVICE_PATH/" > /dev/null
        log_success "主脚本上传完成"
    else
        log_error "主脚本文件不存在: $MAIN_SCRIPT"
        exit 1
    fi
    
    # 上传配置文件
    if [[ -f "$PROJECT_ROOT/src/config.js" ]]; then
        adb push "$PROJECT_ROOT/src/config.js" "$DEVICE_PATH/" > /dev/null
        log_success "配置文件上传完成"
    fi
    
    # 设置文件权限
    adb shell "su -c 'chmod 644 $DEVICE_PATH/*.js'" 2>/dev/null
}

# 检查Frida服务
check_frida_server() {
    log_info "检查Frida服务..."
    
    # 检查frida-server是否运行
    if adb shell "su -c 'pgrep frida-server'" &>/dev/null; then
        log_success "Frida服务正在运行"
        return 0
    else
        log_warning "Frida服务未运行，尝试启动..."
        
        # 尝试启动frida-server
        adb shell "su -c 'frida-server &'" &>/dev/null || {
            log_error "无法启动Frida服务，请手动启动frida-server"
            return 1
        }
        
        sleep 2
        
        if adb shell "su -c 'pgrep frida-server'" &>/dev/null; then
            log_success "Frida服务启动成功"
            return 0
        else
            log_error "Frida服务启动失败"
            return 1
        fi
    fi
}

# 测试Hook连接
test_hook_connection() {
    log_info "测试Hook连接..."
    
    # 检查目标进程
    local target_pid=$(adb shell "su -c 'pgrep com.android.bluetooth'" 2>/dev/null | head -1 | tr -d '\r')
    
    if [[ -z "$target_pid" ]]; then
        log_error "未找到蓝牙进程，请确保蓝牙服务正在运行"
        return 1
    fi
    
    log_info "找到蓝牙进程 PID: $target_pid"
    
    # 测试Frida连接
    if frida -U -p "$target_pid" -e "console.log('Frida连接测试成功')" &>/dev/null; then
        log_success "Frida连接测试通过"
        return 0
    else
        log_error "Frida连接测试失败"
        return 1
    fi
}

# 部署完成提示
deployment_complete() {
    log_success "🎉 部署完成！"
    echo
    log_info "使用方法:"
    echo "  1. 启动监控: ./scripts/monitor.sh"
    echo "  2. 手动运行: frida -U -f com.android.bluetooth -l $DEVICE_PATH/bluetooth-rssi-hook.js --no-pause"
    echo "  3. 查看日志: adb logcat | grep Frida"
    echo
    log_info "输出文件:"
    echo "  - 数据文件: /sdcard/bluetooth_rssi_data.json"
    echo "  - 设备目录: $DEVICE_PATH"
    echo
    log_warning "注意事项:"
    echo "  - 确保蓝牙已开启"
    echo "  - 建议在有蓝牙设备的环境中测试"
    echo "  - 可通过Toast查看实时结果"
}

# 主函数
main() {
    echo "🚀 Frida蓝牙RSSI劫持工具部署脚本"
    echo "适配小米14 Ultra Android 15"
    echo "========================================"
    echo
    
    check_prerequisites
    check_device_info
    check_bluetooth_service
    setup_device_directories
    upload_scripts
    
    if check_frida_server && test_hook_connection; then
        deployment_complete
        exit 0
    else
        log_error "部署过程中出现错误，请检查日志"
        exit 1
    fi
}

# 执行主函数
main "$@"
