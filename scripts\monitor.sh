#!/bin/bash

# Frida蓝牙RSSI监控脚本
# 启动和监控蓝牙RSSI劫持工具

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEVICE_PATH="/sdcard/frida-bluetooth"
MAIN_SCRIPT="bluetooth-rssi-hook.js"
LOG_FILE="/tmp/frida-bluetooth-monitor.log"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_monitor() {
    echo -e "${CYAN}[MONITOR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Frida蓝牙RSSI监控脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -s, --spawn    使用spawn模式启动"
    echo "  -a, --attach   使用attach模式连接"
    echo "  -l, --log      显示详细日志"
    echo "  -t, --test     测试模式（5分钟后自动停止）"
    echo "  --stats        显示统计信息"
    echo "  --stop         停止所有监控进程"
    echo
    echo "示例:"
    echo "  $0              # 默认启动监控"
    echo "  $0 -s           # spawn模式启动"
    echo "  $0 -a           # attach模式连接"
    echo "  $0 --stats      # 查看统计信息"
}

# 检查前置条件
check_prerequisites() {
    if ! command -v adb &> /dev/null; then
        log_error "ADB未找到"
        exit 1
    fi
    
    if ! command -v frida &> /dev/null; then
        log_error "Frida未找到"
        exit 1
    fi
    
    if ! adb devices | grep -q "device$"; then
        log_error "未检测到Android设备"
        exit 1
    fi
}

# 获取蓝牙进程信息
get_bluetooth_process() {
    local bt_pid=$(adb shell "pgrep com.android.bluetooth" 2>/dev/null | head -1 | tr -d '\r')
    
    if [[ -n "$bt_pid" ]]; then
        echo "$bt_pid"
        return 0
    else
        return 1
    fi
}

# 检查脚本文件
check_script_files() {
    log_info "检查脚本文件..."
    
    local script_exists=$(adb shell "test -f $DEVICE_PATH/$MAIN_SCRIPT && echo 'exists'" 2>/dev/null | tr -d '\r')
    
    if [[ "$script_exists" != "exists" ]]; then
        log_error "脚本文件不存在，请先运行部署脚本"
        log_info "运行: ./scripts/deploy.sh"
        exit 1
    fi
    
    log_success "脚本文件检查通过"
}

# Spawn模式启动
start_spawn_mode() {
    log_info "使用Spawn模式启动蓝牙监控..."
    
    local cmd="frida -U -f com.android.bluetooth -l $DEVICE_PATH/$MAIN_SCRIPT --no-pause"
    
    if [[ "$VERBOSE_LOG" == "true" ]]; then
        log_info "执行命令: $cmd"
    fi
    
    $cmd 2>&1 | while IFS= read -r line; do
        log_monitor "$line"
        echo "$(date '+%Y-%m-%d %H:%M:%S') $line" >> "$LOG_FILE"
    done
}

# Attach模式启动
start_attach_mode() {
    log_info "使用Attach模式连接蓝牙进程..."
    
    local bt_pid
    if ! bt_pid=$(get_bluetooth_process); then
        log_error "未找到蓝牙进程，尝试启动蓝牙服务..."
        
        # 尝试启动蓝牙
        adb shell "am start -a android.bluetooth.adapter.action.REQUEST_ENABLE" &>/dev/null || true
        sleep 3
        
        if ! bt_pid=$(get_bluetooth_process); then
            log_error "无法找到或启动蓝牙进程"
            exit 1
        fi
    fi
    
    log_success "找到蓝牙进程 PID: $bt_pid"
    
    local cmd="frida -U -p $bt_pid -l $DEVICE_PATH/$MAIN_SCRIPT"
    
    if [[ "$VERBOSE_LOG" == "true" ]]; then
        log_info "执行命令: $cmd"
    fi
    
    $cmd 2>&1 | while IFS= read -r line; do
        log_monitor "$line"
        echo "$(date '+%Y-%m-%d %H:%M:%S') $line" >> "$LOG_FILE"
    done
}

# 测试模式
start_test_mode() {
    log_info "启动测试模式（5分钟后自动停止）..."
    
    # 启动监控（后台）
    start_attach_mode &
    local monitor_pid=$!
    
    # 5分钟后停止
    sleep 300
    
    log_warning "测试时间结束，停止监控..."
    kill $monitor_pid 2>/dev/null || true
    
    # 显示统计信息
    show_statistics
}

# 显示统计信息
show_statistics() {
    log_info "获取统计信息..."
    
    # 检查数据文件
    local data_file="/sdcard/bluetooth_rssi_data.json"
    local file_exists=$(adb shell "test -f $data_file && echo 'exists'" 2>/dev/null | tr -d '\r')
    
    if [[ "$file_exists" == "exists" ]]; then
        local file_size=$(adb shell "stat -c%s $data_file" 2>/dev/null | tr -d '\r')
        local line_count=$(adb shell "wc -l < $data_file" 2>/dev/null | tr -d '\r')
        
        echo
        log_success "📊 统计信息:"
        echo "  数据文件: $data_file"
        echo "  文件大小: $(( file_size / 1024 )) KB"
        echo "  记录数量: $line_count 条"
        echo
        
        # 显示最近的几条记录
        log_info "最近的记录:"
        adb shell "tail -5 $data_file" 2>/dev/null | while IFS= read -r line; do
            echo "  $line"
        done
    else
        log_warning "未找到数据文件，可能监控尚未开始或出现错误"
    fi
    
    # 显示日志统计
    if [[ -f "$LOG_FILE" ]]; then
        local log_lines=$(wc -l < "$LOG_FILE")
        echo
        log_info "日志统计:"
        echo "  日志文件: $LOG_FILE"
        echo "  日志行数: $log_lines 行"
    fi
}

# 停止所有监控进程
stop_monitoring() {
    log_info "停止所有监控进程..."
    
    # 停止frida进程
    pkill -f "frida.*bluetooth" 2>/dev/null || true
    
    # 停止本脚本的其他实例
    local script_name=$(basename "$0")
    pkill -f "$script_name" 2>/dev/null || true
    
    log_success "监控进程已停止"
}

# 实时显示数据文件内容
tail_data_file() {
    local data_file="/sdcard/bluetooth_rssi_data.json"
    
    log_info "实时显示数据文件内容 (Ctrl+C 停止)..."
    
    # 检查文件是否存在
    local file_exists=$(adb shell "test -f $data_file && echo 'exists'" 2>/dev/null | tr -d '\r')
    
    if [[ "$file_exists" != "exists" ]]; then
        log_warning "数据文件不存在，等待创建..."
        
        # 等待文件创建
        while [[ "$file_exists" != "exists" ]]; do
            sleep 1
            file_exists=$(adb shell "test -f $data_file && echo 'exists'" 2>/dev/null | tr -d '\r')
        done
        
        log_success "数据文件已创建"
    fi
    
    # 实时显示文件内容
    adb shell "tail -f $data_file" 2>/dev/null | while IFS= read -r line; do
        if [[ -n "$line" && "$line" != "//"* ]]; then
            echo -e "${GREEN}[DATA]${NC} $line"
        fi
    done
}

# 主函数
main() {
    local mode="attach"
    local test_mode=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--spawn)
                mode="spawn"
                shift
                ;;
            -a|--attach)
                mode="attach"
                shift
                ;;
            -l|--log)
                VERBOSE_LOG="true"
                shift
                ;;
            -t|--test)
                test_mode=true
                shift
                ;;
            --stats)
                check_prerequisites
                show_statistics
                exit 0
                ;;
            --stop)
                stop_monitoring
                exit 0
                ;;
            --tail)
                check_prerequisites
                tail_data_file
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "🔍 Frida蓝牙RSSI监控工具"
    echo "适配小米14 Ultra Android 15"
    echo "==============================="
    echo
    
    check_prerequisites
    check_script_files
    
    # 清理日志文件
    > "$LOG_FILE"
    
    # 设置信号处理
    trap 'log_warning "收到中断信号，正在停止..."; exit 0' INT TERM
    
    if [[ "$test_mode" == "true" ]]; then
        start_test_mode
    elif [[ "$mode" == "spawn" ]]; then
        start_spawn_mode
    else
        start_attach_mode
    fi
}

# 执行主函数
main "$@"
