#!/bin/bash

# Frida蓝牙RSSI劫持工具测试脚本
# 自动化测试各项功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_DURATION=60  # 测试持续时间（秒）
DATA_FILE="/sdcard/bluetooth_rssi_data.json"

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 测试函数
test_case() {
    local test_name="$1"
    ((TESTS_TOTAL++))
    log_info "测试: $test_name"
}

# 测试1: 检查前置条件
test_prerequisites() {
    test_case "前置条件检查"
    
    # 检查ADB
    if command -v adb &> /dev/null; then
        log_success "ADB工具可用"
    else
        log_error "ADB工具未找到"
        return 1
    fi
    
    # 检查Frida
    if command -v frida &> /dev/null; then
        log_success "Frida工具可用"
    else
        log_error "Frida工具未找到"
        return 1
    fi
    
    # 检查设备连接
    if adb devices | grep -q "device$"; then
        log_success "Android设备已连接"
    else
        log_error "未检测到Android设备"
        return 1
    fi
    
    # 检查root权限
    if adb shell "su -c 'id'" 2>/dev/null | grep -q "uid=0"; then
        log_success "设备具有root权限"
    else
        log_error "设备缺少root权限"
        return 1
    fi
    
    return 0
}

# 测试2: 检查脚本文件
test_script_files() {
    test_case "脚本文件完整性"
    
    local files=(
        "$PROJECT_ROOT/src/bluetooth-rssi-hook.js"
        "$PROJECT_ROOT/src/config.js"
        "$PROJECT_ROOT/scripts/deploy.sh"
        "$PROJECT_ROOT/scripts/monitor.sh"
    )
    
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "文件存在: $(basename "$file")"
        else
            log_error "文件缺失: $file"
            return 1
        fi
    done
    
    return 0
}

# 测试3: 检查蓝牙服务
test_bluetooth_service() {
    test_case "蓝牙服务状态"
    
    # 检查蓝牙进程
    local bt_processes=$(adb shell "ps | grep bluetooth" 2>/dev/null || true)
    
    if echo "$bt_processes" | grep -q "com.android.bluetooth"; then
        log_success "标准蓝牙服务运行中"
    else
        log_error "标准蓝牙服务未运行"
        return 1
    fi
    
    # 检查蓝牙状态
    local bt_enabled=$(adb shell "settings get global bluetooth_on" 2>/dev/null | tr -d '\r')
    if [[ "$bt_enabled" == "1" ]]; then
        log_success "蓝牙已启用"
    else
        log_warning "蓝牙未启用"
    fi
    
    return 0
}

# 测试4: 部署测试
test_deployment() {
    test_case "部署功能"
    
    log_info "执行部署脚本..."
    if "$PROJECT_ROOT/scripts/deploy.sh" &>/dev/null; then
        log_success "部署脚本执行成功"
    else
        log_error "部署脚本执行失败"
        return 1
    fi
    
    # 检查设备上的文件
    local device_script="/sdcard/frida-bluetooth/bluetooth-rssi-hook.js"
    if adb shell "test -f $device_script" 2>/dev/null; then
        log_success "脚本文件已上传到设备"
    else
        log_error "脚本文件未上传到设备"
        return 1
    fi
    
    return 0
}

# 测试5: Frida连接测试
test_frida_connection() {
    test_case "Frida连接"
    
    # 获取蓝牙进程PID
    local bt_pid=$(adb shell "pgrep com.android.bluetooth" 2>/dev/null | head -1 | tr -d '\r')
    
    if [[ -z "$bt_pid" ]]; then
        log_error "未找到蓝牙进程"
        return 1
    fi
    
    log_info "蓝牙进程PID: $bt_pid"
    
    # 测试Frida连接
    if timeout 10 frida -U -p "$bt_pid" -e "console.log('Frida连接测试')" &>/dev/null; then
        log_success "Frida连接正常"
    else
        log_error "Frida连接失败"
        return 1
    fi
    
    return 0
}

# 测试6: Hook功能测试
test_hook_functionality() {
    test_case "Hook功能"
    
    log_info "启动Hook测试（${TEST_DURATION}秒）..."
    
    # 清理旧的数据文件
    adb shell "rm -f $DATA_FILE" 2>/dev/null || true
    
    # 启动监控（后台）
    timeout $TEST_DURATION "$PROJECT_ROOT/scripts/monitor.sh" -a &>/dev/null &
    local monitor_pid=$!
    
    # 等待测试完成
    sleep $TEST_DURATION
    
    # 停止监控
    kill $monitor_pid 2>/dev/null || true
    sleep 2
    
    # 检查结果
    if adb shell "test -f $DATA_FILE" 2>/dev/null; then
        local file_size=$(adb shell "stat -c%s $DATA_FILE" 2>/dev/null | tr -d '\r')
        local line_count=$(adb shell "wc -l < $DATA_FILE" 2>/dev/null | tr -d '\r')
        
        if [[ "$file_size" -gt 0 && "$line_count" -gt 0 ]]; then
            log_success "Hook功能正常，生成了 $line_count 条记录"
        else
            log_error "Hook功能异常，未生成有效数据"
            return 1
        fi
    else
        log_error "Hook功能失败，未生成数据文件"
        return 1
    fi
    
    return 0
}

# 测试7: 数据格式验证
test_data_format() {
    test_case "数据格式验证"
    
    if ! adb shell "test -f $DATA_FILE" 2>/dev/null; then
        log_error "数据文件不存在"
        return 1
    fi
    
    # 获取第一条有效数据
    local sample_data=$(adb shell "grep -v '^//' $DATA_FILE | head -1" 2>/dev/null | tr -d '\r')
    
    if [[ -z "$sample_data" ]]; then
        log_error "未找到有效数据"
        return 1
    fi
    
    # 验证JSON格式
    if echo "$sample_data" | python3 -m json.tool &>/dev/null; then
        log_success "数据格式为有效JSON"
    else
        log_error "数据格式不是有效JSON"
        return 1
    fi
    
    # 验证必要字段
    local required_fields=("timestamp" "device_name" "mac_address" "rssi" "device_type")
    
    for field in "${required_fields[@]}"; do
        if echo "$sample_data" | grep -q "\"$field\""; then
            log_success "包含必要字段: $field"
        else
            log_error "缺少必要字段: $field"
            return 1
        fi
    done
    
    return 0
}

# 测试8: 性能测试
test_performance() {
    test_case "性能测试"
    
    log_info "检查系统资源使用..."
    
    # 检查CPU使用率
    local cpu_usage=$(adb shell "top -n 1 | grep frida" 2>/dev/null | awk '{print $9}' | head -1 | tr -d '%\r')
    
    if [[ -n "$cpu_usage" && "$cpu_usage" -lt 20 ]]; then
        log_success "CPU使用率正常: ${cpu_usage}%"
    else
        log_warning "CPU使用率较高或无法检测"
    fi
    
    # 检查内存使用
    local memory_usage=$(adb shell "dumpsys meminfo | grep frida" 2>/dev/null | awk '{print $2}' | head -1)
    
    if [[ -n "$memory_usage" ]]; then
        local memory_mb=$((memory_usage / 1024))
        if [[ "$memory_mb" -lt 100 ]]; then
            log_success "内存使用正常: ${memory_mb}MB"
        else
            log_warning "内存使用较高: ${memory_mb}MB"
        fi
    else
        log_warning "无法检测内存使用"
    fi
    
    return 0
}

# 清理测试环境
cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 停止所有frida进程
    pkill -f "frida.*bluetooth" 2>/dev/null || true
    
    # 清理测试数据（可选）
    # adb shell "rm -f $DATA_FILE" 2>/dev/null || true
    
    log_info "清理完成"
}

# 显示测试报告
show_test_report() {
    echo
    echo "==============================="
    echo "🧪 测试报告"
    echo "==============================="
    echo "总测试数: $TESTS_TOTAL"
    echo "通过数量: $TESTS_PASSED"
    echo "失败数量: $TESTS_FAILED"
    echo
    
    if [[ "$TESTS_FAILED" -eq 0 ]]; then
        log_success "🎉 所有测试通过！"
        echo
        log_info "系统已准备就绪，可以开始使用："
        echo "  ./scripts/monitor.sh    # 启动监控"
        echo "  ./scripts/monitor.sh --stats  # 查看统计"
    else
        log_error "❌ 有 $TESTS_FAILED 个测试失败"
        echo
        log_info "请检查失败的测试项目并修复问题"
    fi
    
    echo "==============================="
}

# 主函数
main() {
    echo "🧪 Frida蓝牙RSSI劫持工具测试套件"
    echo "适配小米14 Ultra Android 15"
    echo "==============================="
    echo
    
    # 设置信号处理
    trap 'log_warning "测试被中断"; cleanup_test_environment; exit 1' INT TERM
    
    # 执行测试
    test_prerequisites || true
    test_script_files || true
    test_bluetooth_service || true
    test_deployment || true
    test_frida_connection || true
    test_hook_functionality || true
    test_data_format || true
    test_performance || true
    
    # 清理环境
    cleanup_test_environment
    
    # 显示报告
    show_test_report
    
    # 返回适当的退出码
    if [[ "$TESTS_FAILED" -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
