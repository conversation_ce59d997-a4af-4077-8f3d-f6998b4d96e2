/**
 * Frida蓝牙RSSI劫持主脚本
 * 适配小米14 Ultra Android 15
 * 
 * 使用方法：
 * frida -U -f com.android.bluetooth -l bluetooth-rssi-hook.js --no-pause
 */

// 加载配置和工具模块
Java.perform(function() {
    console.log("🚀 Frida蓝牙RSSI劫持工具启动中...");
    
    // 加载配置
    const CONFIG = {
        VERSION: "1.0.0",
        DEBUG: true,
        TARGET_PROCESSES: ["com.android.bluetooth", "com.miui.bluetooth"],
        SCAN_INTERVAL: 1000,
        RSSI_THRESHOLD: -100,
        DUPLICATE_FILTER_WINDOW: 3000,
        RSSI_CHANGE_THRESHOLD: 5,
        OUTPUT: {
            ENABLE_FILE: true,
            ENABLE_TOAST: true,
            ENABLE_CONSOLE: true,
            FILE_PATH: "/sdcard/bluetooth_rssi_data.json",
            MAX_FILE_SIZE: 10 * 1024 * 1024,
            BACKUP_COUNT: 5,
            TOAST_DURATION: 2000,
            TOAST_DEBOUNCE: 3000,
            TOAST_MAX_DEVICES: 3
        },
        PERFORMANCE: {
            MAX_CACHE_SIZE: 100,
            BATCH_WRITE_SIZE: 10,
            MEMORY_CLEANUP_INTERVAL: 60000
        }
    };

    // 全局变量
    let dataProcessor = null;
    let fileWriter = null;
    let toastHelper = null;
    let hookCount = 0;
    let startTime = Date.now();

    /**
     * 数据处理器类（简化版）
     */
    class SimpleDataProcessor {
        constructor() {
            this.deviceCache = new Map();
            this.lastCleanup = Date.now();
        }

        processScanResult(scanResult) {
            try {
                const device = scanResult.getDevice();
                const rssi = scanResult.getRssi();
                
                const data = {
                    timestamp: new Date().toISOString(),
                    device_name: this.getDeviceName(device),
                    mac_address: device.getAddress(),
                    rssi: rssi,
                    device_type: "BLE"
                };

                if (this.shouldRecord(data)) {
                    this.updateCache(data);
                    return data;
                }
                return null;
            } catch (error) {
                console.error("[DataProcessor] Error:", error.message);
                return null;
            }
        }

        getDeviceName(device) {
            try {
                let name = device.getName();
                return name || device.getAddress() || "Unknown";
            } catch (error) {
                return "Unknown";
            }
        }

        shouldRecord(data) {
            if (data.rssi < CONFIG.RSSI_THRESHOLD || data.rssi > 0) return false;
            
            const key = data.mac_address;
            const now = Date.now();
            
            if (!this.deviceCache.has(key)) return true;
            
            const cached = this.deviceCache.get(key);
            const timeDiff = now - cached.timestamp;
            const rssiDiff = Math.abs(data.rssi - cached.rssi);
            
            return timeDiff >= CONFIG.DUPLICATE_FILTER_WINDOW || 
                   rssiDiff >= CONFIG.RSSI_CHANGE_THRESHOLD;
        }

        updateCache(data) {
            this.deviceCache.set(data.mac_address, {
                timestamp: Date.now(),
                rssi: data.rssi
            });
        }
    }

    /**
     * 文件写入器类（简化版）
     */
    class SimpleFileWriter {
        constructor() {
            this.writeQueue = [];
            this.writeCount = 0;
        }

        async writeData(data) {
            if (!CONFIG.OUTPUT.ENABLE_FILE) return;
            
            try {
                const content = JSON.stringify(data) + '\n';
                await this.appendToFile(content);
                this.writeCount++;
                
                if (CONFIG.DEBUG && this.writeCount % 10 === 0) {
                    console.log(`[FileWriter] Total records written: ${this.writeCount}`);
                }
            } catch (error) {
                console.error("[FileWriter] Error:", error.message);
            }
        }

        async appendToFile(content) {
            try {
                const File = Java.use("java.io.File");
                const FileWriter = Java.use("java.io.FileWriter");
                const BufferedWriter = Java.use("java.io.BufferedWriter");
                
                const file = File.$new(CONFIG.OUTPUT.FILE_PATH);
                const fileWriter = FileWriter.$new(file, true);
                const bufferedWriter = BufferedWriter.$new(fileWriter);
                
                bufferedWriter.write(content);
                bufferedWriter.flush();
                bufferedWriter.close();
                fileWriter.close();
            } catch (error) {
                throw error;
            }
        }
    }

    /**
     * Toast助手类（简化版）
     */
    class SimpleToastHelper {
        constructor() {
            this.lastToastTime = new Map();
            this.initializeToast();
        }

        initializeToast() {
            try {
                this.Toast = Java.use("android.widget.Toast");
                this.ActivityThread = Java.use("android.app.ActivityThread");
                this.context = this.ActivityThread.currentApplication().getApplicationContext();
            } catch (error) {
                console.error("[ToastHelper] Init error:", error.message);
                this.context = null;
            }
        }

        showDeviceToast(deviceData) {
            if (!CONFIG.OUTPUT.ENABLE_TOAST || !this.context) return;
            
            try {
                const deviceKey = deviceData.mac_address;
                const now = Date.now();
                
                if (this.shouldSkipToast(deviceKey, now)) return;
                
                this.lastToastTime.set(deviceKey, now);
                
                const message = `🔵 ${deviceData.device_name}\nRSSI: ${deviceData.rssi}dBm`;
                
                Java.scheduleOnMainThread(() => {
                    try {
                        const toast = this.Toast.makeText(
                            this.context,
                            message,
                            this.Toast.LENGTH_SHORT.value
                        );
                        toast.show();
                    } catch (error) {
                        console.error("[Toast] Display error:", error.message);
                    }
                });
            } catch (error) {
                console.error("[ToastHelper] Error:", error.message);
            }
        }

        shouldSkipToast(deviceKey, currentTime) {
            if (!this.lastToastTime.has(deviceKey)) return false;
            
            const lastTime = this.lastToastTime.get(deviceKey);
            return (currentTime - lastTime) < CONFIG.OUTPUT.TOAST_DEBOUNCE;
        }
    }

    /**
     * 初始化所有模块
     */
    function initializeModules() {
        try {
            dataProcessor = new SimpleDataProcessor();
            fileWriter = new SimpleFileWriter();
            toastHelper = new SimpleToastHelper();
            
            console.log("✅ 所有模块初始化完成");
            return true;
        } catch (error) {
            console.error("❌ 模块初始化失败:", error.message);
            return false;
        }
    }

    /**
     * 处理扫描结果
     */
    function handleScanResult(scanResult) {
        try {
            hookCount++;
            
            const processedData = dataProcessor.processScanResult(scanResult);
            if (!processedData) return;

            // 控制台输出
            if (CONFIG.OUTPUT.ENABLE_CONSOLE) {
                console.log(`📱 [${hookCount}] ${processedData.device_name} | ${processedData.rssi}dBm | ${processedData.mac_address}`);
            }

            // 文件写入
            fileWriter.writeData(processedData);

            // Toast显示
            toastHelper.showDeviceToast(processedData);

        } catch (error) {
            console.error("[HandleScanResult] Error:", error.message);
        }
    }

    /**
     * Hook ScanCallback.onScanResult
     */
    function hookScanCallback() {
        try {
            const ScanCallback = Java.use("android.bluetooth.le.ScanCallback");
            
            ScanCallback.onScanResult.overload('int', 'android.bluetooth.le.ScanResult').implementation = function(callbackType, result) {
                // 调用原始方法
                this.onScanResult(callbackType, result);
                
                // 处理扫描结果
                handleScanResult(result);
            };
            
            console.log("✅ ScanCallback.onScanResult Hook成功");
            return true;
        } catch (error) {
            console.error("❌ ScanCallback Hook失败:", error.message);
            return false;
        }
    }

    /**
     * Hook BluetoothLeScanner.startScan (监控扫描启动)
     */
    function hookBluetoothLeScanner() {
        try {
            const BluetoothLeScanner = Java.use("android.bluetooth.le.BluetoothLeScanner");
            
            BluetoothLeScanner.startScan.overload('android.bluetooth.le.ScanCallback').implementation = function(callback) {
                console.log("🔍 蓝牙扫描启动 - startScan(callback)");
                return this.startScan(callback);
            };
            
            BluetoothLeScanner.startScan.overload('java.util.List', 'android.bluetooth.le.ScanSettings', 'android.bluetooth.le.ScanCallback').implementation = function(filters, settings, callback) {
                console.log("🔍 蓝牙扫描启动 - startScan(filters, settings, callback)");
                return this.startScan(filters, settings, callback);
            };
            
            console.log("✅ BluetoothLeScanner Hook成功");
            return true;
        } catch (error) {
            console.error("❌ BluetoothLeScanner Hook失败:", error.message);
            return false;
        }
    }

    /**
     * 主要初始化函数
     */
    function main() {
        console.log(`📋 配置信息:`);
        console.log(`   - 版本: ${CONFIG.VERSION}`);
        console.log(`   - RSSI阈值: ${CONFIG.RSSI_THRESHOLD}dBm`);
        console.log(`   - 文件路径: ${CONFIG.OUTPUT.FILE_PATH}`);
        console.log(`   - Toast: ${CONFIG.OUTPUT.ENABLE_TOAST ? '启用' : '禁用'}`);
        
        // 初始化模块
        if (!initializeModules()) {
            console.error("❌ 初始化失败，退出");
            return;
        }

        // 设置Hook
        let hookSuccess = 0;
        
        if (hookScanCallback()) hookSuccess++;
        if (hookBluetoothLeScanner()) hookSuccess++;
        
        if (hookSuccess > 0) {
            console.log(`🎯 Hook设置完成 (${hookSuccess}/2)`);
            console.log("🚀 蓝牙RSSI监控已启动，等待扫描结果...");
            
            // 定期统计信息
            setInterval(() => {
                const runtime = Math.floor((Date.now() - startTime) / 1000);
                console.log(`📊 运行时间: ${runtime}s | Hook次数: ${hookCount} | 写入记录: ${fileWriter.writeCount}`);
            }, 30000);
            
        } else {
            console.error("❌ 所有Hook都失败了");
        }
    }

    // 启动主程序
    setTimeout(main, 1000);
});

console.log("🔧 Frida脚本加载完成，等待Java环境初始化...");
