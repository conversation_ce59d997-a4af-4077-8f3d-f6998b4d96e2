/**
 * Frida蓝牙RSSI劫持工具配置文件
 * 适配小米14 Ultra Android 15
 */

const CONFIG = {
    // 基础配置
    VERSION: "1.0.0",
    DEBUG: true,
    
    // 目标进程配置
    TARGET_PROCESSES: [
        "com.android.bluetooth",    // 主要目标
        "com.miui.bluetooth"        // 小米定制蓝牙服务
    ],
    
    // Hook配置
    HOOK_TARGETS: {
        SCAN_CALLBACK: "android.bluetooth.le.ScanCallback",
        BLUETOOTH_DEVICE: "android.bluetooth.BluetoothDevice",
        BLUETOOTH_LE_SCANNER: "android.bluetooth.le.BluetoothLeScanner"
    },
    
    // 数据采集配置
    SCAN_INTERVAL: 1000,           // 扫描间隔(ms)
    RSSI_THRESHOLD: -100,          // RSSI阈值过滤
    DUPLICATE_FILTER_WINDOW: 3000, // 去重时间窗口(ms)
    RSSI_CHANGE_THRESHOLD: 5,      // RSSI变化阈值(dBm)
    
    // 输出配置
    OUTPUT: {
        ENABLE_FILE: true,
        ENABLE_TOAST: true,
        ENABLE_CONSOLE: true,
        
        // 文件配置
        FILE_PATH: "/sdcard/bluetooth_rssi_data.json",
        MAX_FILE_SIZE: 10 * 1024 * 1024,  // 10MB
        BACKUP_COUNT: 5,
        
        // Toast配置
        TOAST_DURATION: 2000,      // Toast显示时长(ms)
        TOAST_DEBOUNCE: 3000,      // Toast防抖时间(ms)
        TOAST_MAX_DEVICES: 3       // 同时显示的最大设备数
    },
    
    // 性能配置
    PERFORMANCE: {
        MAX_CACHE_SIZE: 100,       // 最大缓存设备数
        BATCH_WRITE_SIZE: 10,      // 批量写入大小
        MEMORY_CLEANUP_INTERVAL: 60000  // 内存清理间隔(ms)
    },
    
    // 错误处理配置
    ERROR_HANDLING: {
        MAX_RETRY_COUNT: 3,
        RETRY_DELAY: 1000,         // 重试延迟(ms)
        CRASH_RECOVERY: true,
        AUTO_RESTART: true
    },
    
    // 日志配置
    LOGGING: {
        LEVEL: "INFO",             // DEBUG, INFO, WARN, ERROR
        MAX_LOG_SIZE: 1024 * 1024, // 1MB
        ENABLE_TIMESTAMP: true
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else if (typeof global !== 'undefined') {
    global.CONFIG = CONFIG;
}
