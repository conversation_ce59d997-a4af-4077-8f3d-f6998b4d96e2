/**
 * 数据处理模块
 * 负责RSSI数据的提取、格式化、去重和验证
 */

class DataProcessor {
    constructor(config) {
        this.config = config;
        this.deviceCache = new Map();
        this.lastCleanup = Date.now();
    }

    /**
     * 处理扫描结果数据
     * @param {Object} scanResult - Android ScanResult对象
     * @returns {Object|null} 处理后的数据对象
     */
    processScanResult(scanResult) {
        try {
            // 提取基础数据
            const rawData = this.extractRawData(scanResult);
            if (!rawData) return null;

            // 数据验证
            if (!this.validateData(rawData)) return null;

            // 去重过滤
            if (!this.shouldRecord(rawData)) return null;

            // 格式化输出
            const formattedData = this.formatData(rawData);
            
            // 更新缓存
            this.updateCache(rawData);
            
            // 定期清理缓存
            this.cleanupCache();
            
            return formattedData;
        } catch (error) {
            this.logError("processScanResult", error);
            return null;
        }
    }

    /**
     * 从ScanResult提取原始数据
     */
    extractRawData(scanResult) {
        try {
            const device = scanResult.getDevice();
            const rssi = scanResult.getRssi();
            const scanRecord = scanResult.getScanRecord();
            
            return {
                macAddress: device.getAddress(),
                deviceName: this.getDeviceName(device),
                rssi: rssi,
                timestamp: Date.now(),
                scanRecord: scanRecord ? scanRecord.getBytes() : null,
                deviceType: this.getDeviceType(device)
            };
        } catch (error) {
            this.logError("extractRawData", error);
            return null;
        }
    }

    /**
     * 获取设备名称（优先友好名称，备选MAC地址）
     */
    getDeviceName(device) {
        try {
            let name = device.getName();
            if (!name || name.trim() === "") {
                name = device.getAddress();
            }
            return name;
        } catch (error) {
            return device.getAddress() || "Unknown";
        }
    }

    /**
     * 获取设备类型
     */
    getDeviceType(device) {
        try {
            const deviceClass = device.getBluetoothClass();
            if (deviceClass) {
                return "Classic";
            }
            return "BLE";
        } catch (error) {
            return "Unknown";
        }
    }

    /**
     * 数据验证
     */
    validateData(data) {
        if (!data.macAddress || !data.rssi) return false;
        if (data.rssi < this.config.RSSI_THRESHOLD) return false;
        if (data.rssi > 0) return false; // RSSI不应为正值
        return true;
    }

    /**
     * 去重判断 - 是否应该记录此数据
     */
    shouldRecord(data) {
        const key = data.macAddress;
        const now = data.timestamp;
        
        if (!this.deviceCache.has(key)) {
            return true;
        }
        
        const cached = this.deviceCache.get(key);
        const timeDiff = now - cached.timestamp;
        const rssiDiff = Math.abs(data.rssi - cached.rssi);
        
        // 时间窗口内且RSSI变化不大，不记录
        if (timeDiff < this.config.DUPLICATE_FILTER_WINDOW && 
            rssiDiff < this.config.RSSI_CHANGE_THRESHOLD) {
            return false;
        }
        
        return true;
    }

    /**
     * 格式化数据为输出格式
     */
    formatData(data) {
        return {
            timestamp: new Date(data.timestamp).toISOString(),
            device_name: data.deviceName,
            mac_address: data.macAddress,
            rssi: data.rssi,
            device_type: data.deviceType
        };
    }

    /**
     * 更新设备缓存
     */
    updateCache(data) {
        this.deviceCache.set(data.macAddress, {
            timestamp: data.timestamp,
            rssi: data.rssi,
            deviceName: data.deviceName
        });
    }

    /**
     * 清理过期缓存
     */
    cleanupCache() {
        const now = Date.now();
        if (now - this.lastCleanup < this.config.PERFORMANCE.MEMORY_CLEANUP_INTERVAL) {
            return;
        }
        
        const expireTime = now - this.config.DUPLICATE_FILTER_WINDOW * 2;
        for (const [key, value] of this.deviceCache.entries()) {
            if (value.timestamp < expireTime) {
                this.deviceCache.delete(key);
            }
        }
        
        // 限制缓存大小
        if (this.deviceCache.size > this.config.PERFORMANCE.MAX_CACHE_SIZE) {
            const entries = Array.from(this.deviceCache.entries());
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
            const toDelete = entries.slice(0, entries.length - this.config.PERFORMANCE.MAX_CACHE_SIZE);
            toDelete.forEach(([key]) => this.deviceCache.delete(key));
        }
        
        this.lastCleanup = now;
    }

    /**
     * 错误日志
     */
    logError(method, error) {
        console.error(`[DataProcessor.${method}] Error:`, error.message);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            cacheSize: this.deviceCache.size,
            lastCleanup: new Date(this.lastCleanup).toISOString()
        };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataProcessor;
} else if (typeof global !== 'undefined') {
    global.DataProcessor = DataProcessor;
}
