/**
 * 文件写入模块
 * 负责数据的异步文件写入、文件轮转和错误处理
 */

class FileWriter {
    constructor(config) {
        this.config = config;
        this.writeQueue = [];
        this.isWriting = false;
        this.currentFilePath = this.generateFilePath();
        this.writeCount = 0;
        this.lastRotation = Date.now();
        
        this.initializeFile();
    }

    /**
     * 添加数据到写入队列
     * @param {Object} data - 要写入的数据对象
     */
    async writeData(data) {
        if (!this.config.OUTPUT.ENABLE_FILE) return;
        
        try {
            this.writeQueue.push(data);
            
            // 批量写入控制
            if (this.writeQueue.length >= this.config.PERFORMANCE.BATCH_WRITE_SIZE || 
                !this.isWriting) {
                await this.flushQueue();
            }
        } catch (error) {
            this.logError("writeData", error);
        }
    }

    /**
     * 刷新写入队列
     */
    async flushQueue() {
        if (this.isWriting || this.writeQueue.length === 0) return;
        
        this.isWriting = true;
        
        try {
            // 检查文件轮转
            await this.checkRotation();
            
            // 批量写入
            const batch = this.writeQueue.splice(0, this.config.PERFORMANCE.BATCH_WRITE_SIZE);
            const content = batch.map(data => JSON.stringify(data)).join('\n') + '\n';
            
            await this.appendToFile(content);
            this.writeCount += batch.length;
            
            if (this.config.DEBUG) {
                console.log(`[FileWriter] Wrote ${batch.length} records, total: ${this.writeCount}`);
            }
            
        } catch (error) {
            this.logError("flushQueue", error);
            // 写入失败，重新加入队列
            this.writeQueue.unshift(...batch);
        } finally {
            this.isWriting = false;
            
            // 如果队列还有数据，继续写入
            if (this.writeQueue.length > 0) {
                setTimeout(() => this.flushQueue(), 100);
            }
        }
    }

    /**
     * 检查是否需要文件轮转
     */
    async checkRotation() {
        try {
            const fileSize = await this.getFileSize(this.currentFilePath);
            
            if (fileSize > this.config.OUTPUT.MAX_FILE_SIZE) {
                await this.rotateFile();
            }
        } catch (error) {
            // 文件不存在或其他错误，重新初始化
            this.initializeFile();
        }
    }

    /**
     * 文件轮转
     */
    async rotateFile() {
        try {
            // 生成新文件路径
            const newFilePath = this.generateFilePath();
            
            // 备份当前文件
            const backupPath = this.generateBackupPath();
            await this.moveFile(this.currentFilePath, backupPath);
            
            // 更新当前文件路径
            this.currentFilePath = newFilePath;
            this.writeCount = 0;
            this.lastRotation = Date.now();
            
            // 初始化新文件
            this.initializeFile();
            
            // 清理旧备份
            await this.cleanupOldBackups();
            
            console.log(`[FileWriter] File rotated to: ${this.currentFilePath}`);
            
        } catch (error) {
            this.logError("rotateFile", error);
        }
    }

    /**
     * 生成文件路径
     */
    generateFilePath() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const basePath = this.config.OUTPUT.FILE_PATH.replace('.json', '');
        return `${basePath}_${timestamp}.json`;
    }

    /**
     * 生成备份文件路径
     */
    generateBackupPath() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const basePath = this.config.OUTPUT.FILE_PATH.replace('.json', '');
        return `${basePath}_backup_${timestamp}.json`;
    }

    /**
     * 初始化文件
     */
    initializeFile() {
        try {
            const header = {
                version: this.config.VERSION,
                created: new Date().toISOString(),
                device: "Xiaomi 14 Ultra",
                android_version: "15",
                description: "Bluetooth RSSI data captured by Frida"
            };
            
            this.appendToFile(`// ${JSON.stringify(header)}\n`);
        } catch (error) {
            this.logError("initializeFile", error);
        }
    }

    /**
     * 追加内容到文件
     */
    async appendToFile(content) {
        return new Promise((resolve, reject) => {
            try {
                // 使用Java File API进行文件操作
                const File = Java.use("java.io.File");
                const FileWriter = Java.use("java.io.FileWriter");
                const BufferedWriter = Java.use("java.io.BufferedWriter");
                
                const file = File.$new(this.currentFilePath);
                const fileWriter = FileWriter.$new(file, true); // append mode
                const bufferedWriter = BufferedWriter.$new(fileWriter);
                
                bufferedWriter.write(content);
                bufferedWriter.flush();
                bufferedWriter.close();
                fileWriter.close();
                
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 获取文件大小
     */
    async getFileSize(filePath) {
        return new Promise((resolve, reject) => {
            try {
                const File = Java.use("java.io.File");
                const file = File.$new(filePath);
                
                if (file.exists()) {
                    resolve(file.length());
                } else {
                    resolve(0);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 移动文件
     */
    async moveFile(sourcePath, destPath) {
        return new Promise((resolve, reject) => {
            try {
                const File = Java.use("java.io.File");
                const sourceFile = File.$new(sourcePath);
                const destFile = File.$new(destPath);
                
                if (sourceFile.exists()) {
                    sourceFile.renameTo(destFile);
                }
                
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 清理旧备份文件
     */
    async cleanupOldBackups() {
        try {
            const File = Java.use("java.io.File");
            const basePath = this.config.OUTPUT.FILE_PATH.replace('.json', '');
            const parentDir = File.$new(basePath).getParent();
            const dir = File.$new(parentDir);
            
            if (dir.exists() && dir.isDirectory()) {
                const files = dir.listFiles();
                const backupFiles = [];
                
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    if (file.getName().includes('_backup_')) {
                        backupFiles.push({
                            file: file,
                            lastModified: file.lastModified()
                        });
                    }
                }
                
                // 按修改时间排序，删除最旧的文件
                backupFiles.sort((a, b) => b.lastModified - a.lastModified);
                
                if (backupFiles.length > this.config.OUTPUT.BACKUP_COUNT) {
                    const toDelete = backupFiles.slice(this.config.OUTPUT.BACKUP_COUNT);
                    toDelete.forEach(item => {
                        item.file.delete();
                        console.log(`[FileWriter] Deleted old backup: ${item.file.getName()}`);
                    });
                }
            }
        } catch (error) {
            this.logError("cleanupOldBackups", error);
        }
    }

    /**
     * 强制刷新所有待写入数据
     */
    async forceFlush() {
        while (this.writeQueue.length > 0) {
            await this.flushQueue();
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }

    /**
     * 错误日志
     */
    logError(method, error) {
        console.error(`[FileWriter.${method}] Error:`, error.message);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            currentFile: this.currentFilePath,
            writeCount: this.writeCount,
            queueSize: this.writeQueue.length,
            lastRotation: new Date(this.lastRotation).toISOString()
        };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileWriter;
} else if (typeof global !== 'undefined') {
    global.FileWriter = FileWriter;
}
