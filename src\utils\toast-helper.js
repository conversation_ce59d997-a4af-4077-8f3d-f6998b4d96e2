/**
 * Toast显示模块
 * 负责Android Toast消息的显示和防抖控制
 */

class ToastHelper {
    constructor(config) {
        this.config = config;
        this.lastToastTime = new Map();
        this.toastQueue = [];
        this.isShowingToast = false;
        
        this.initializeToast();
    }

    /**
     * 初始化Toast相关的Java类
     */
    initializeToast() {
        try {
            this.Toast = Java.use("android.widget.Toast");
            this.Context = Java.use("android.content.Context");
            this.ActivityThread = Java.use("android.app.ActivityThread");
            
            // 获取应用上下文
            this.context = this.getApplicationContext();
            
            if (this.config.DEBUG) {
                console.log("[ToastHelper] Initialized successfully");
            }
        } catch (error) {
            this.logError("initializeToast", error);
        }
    }

    /**
     * 获取应用上下文
     */
    getApplicationContext() {
        try {
            const currentApplication = this.ActivityThread.currentApplication();
            if (currentApplication) {
                return currentApplication.getApplicationContext();
            }
            
            // 备选方案：尝试获取系统上下文
            const ActivityManagerService = Java.use("com.android.server.am.ActivityManagerService");
            return ActivityManagerService.getContext();
        } catch (error) {
            this.logError("getApplicationContext", error);
            return null;
        }
    }

    /**
     * 显示蓝牙设备信息Toast
     * @param {Object} deviceData - 设备数据对象
     */
    showDeviceToast(deviceData) {
        if (!this.config.OUTPUT.ENABLE_TOAST || !this.context) return;
        
        try {
            const deviceKey = deviceData.mac_address;
            const now = Date.now();
            
            // 防抖检查
            if (this.shouldSkipToast(deviceKey, now)) return;
            
            // 添加到Toast队列
            this.toastQueue.push({
                deviceData: deviceData,
                timestamp: now
            });
            
            // 更新最后显示时间
            this.lastToastTime.set(deviceKey, now);
            
            // 处理Toast队列
            this.processToastQueue();
            
        } catch (error) {
            this.logError("showDeviceToast", error);
        }
    }

    /**
     * 检查是否应该跳过Toast显示
     */
    shouldSkipToast(deviceKey, currentTime) {
        if (!this.lastToastTime.has(deviceKey)) return false;
        
        const lastTime = this.lastToastTime.get(deviceKey);
        const timeDiff = currentTime - lastTime;
        
        return timeDiff < this.config.OUTPUT.TOAST_DEBOUNCE;
    }

    /**
     * 处理Toast显示队列
     */
    async processToastQueue() {
        if (this.isShowingToast || this.toastQueue.length === 0) return;
        
        this.isShowingToast = true;
        
        try {
            // 获取最新的几个设备（按配置限制数量）
            const recentDevices = this.getRecentDevices();
            
            if (recentDevices.length > 0) {
                const message = this.formatToastMessage(recentDevices);
                await this.displayToast(message);
            }
            
            // 清空队列
            this.toastQueue = [];
            
        } catch (error) {
            this.logError("processToastQueue", error);
        } finally {
            this.isShowingToast = false;
        }
    }

    /**
     * 获取最近的设备数据
     */
    getRecentDevices() {
        // 按时间戳排序，获取最新的设备
        const sorted = this.toastQueue.sort((a, b) => b.timestamp - a.timestamp);
        
        // 去重（相同MAC地址只保留最新的）
        const uniqueDevices = new Map();
        sorted.forEach(item => {
            const mac = item.deviceData.mac_address;
            if (!uniqueDevices.has(mac)) {
                uniqueDevices.set(mac, item.deviceData);
            }
        });
        
        // 限制显示数量
        const devices = Array.from(uniqueDevices.values());
        return devices.slice(0, this.config.OUTPUT.TOAST_MAX_DEVICES);
    }

    /**
     * 格式化Toast消息
     */
    formatToastMessage(devices) {
        if (devices.length === 1) {
            const device = devices[0];
            return `🔵 ${device.device_name}\nRSSI: ${device.rssi}dBm`;
        } else {
            const deviceList = devices.map(device => 
                `${device.device_name}: ${device.rssi}dBm`
            ).join('\n');
            return `🔵 发现 ${devices.length} 个蓝牙设备:\n${deviceList}`;
        }
    }

    /**
     * 显示Toast消息
     */
    async displayToast(message) {
        return new Promise((resolve) => {
            try {
                Java.scheduleOnMainThread(() => {
                    try {
                        const toast = this.Toast.makeText(
                            this.context,
                            message,
                            this.Toast.LENGTH_SHORT.value
                        );
                        toast.show();
                        
                        if (this.config.DEBUG) {
                            console.log(`[ToastHelper] Displayed: ${message}`);
                        }
                    } catch (error) {
                        this.logError("displayToast.mainThread", error);
                    }
                    resolve();
                });
            } catch (error) {
                this.logError("displayToast", error);
                resolve();
            }
        });
    }

    /**
     * 显示统计信息Toast
     */
    showStatsToast(stats) {
        if (!this.config.OUTPUT.ENABLE_TOAST) return;
        
        try {
            const message = `📊 蓝牙监控统计\n` +
                          `设备缓存: ${stats.cacheSize}\n` +
                          `写入记录: ${stats.writeCount}\n` +
                          `队列大小: ${stats.queueSize}`;
            
            Java.scheduleOnMainThread(() => {
                try {
                    const toast = this.Toast.makeText(
                        this.context,
                        message,
                        this.Toast.LENGTH_LONG.value
                    );
                    toast.show();
                } catch (error) {
                    this.logError("showStatsToast.mainThread", error);
                }
            });
        } catch (error) {
            this.logError("showStatsToast", error);
        }
    }

    /**
     * 显示错误信息Toast
     */
    showErrorToast(errorMessage) {
        if (!this.config.OUTPUT.ENABLE_TOAST) return;
        
        try {
            const message = `❌ 蓝牙监控错误\n${errorMessage}`;
            
            Java.scheduleOnMainThread(() => {
                try {
                    const toast = this.Toast.makeText(
                        this.context,
                        message,
                        this.Toast.LENGTH_LONG.value
                    );
                    toast.show();
                } catch (error) {
                    this.logError("showErrorToast.mainThread", error);
                }
            });
        } catch (error) {
            this.logError("showErrorToast", error);
        }
    }

    /**
     * 清理过期的Toast时间记录
     */
    cleanupToastTimes() {
        const now = Date.now();
        const expireTime = now - this.config.OUTPUT.TOAST_DEBOUNCE * 2;
        
        for (const [key, time] of this.lastToastTime.entries()) {
            if (time < expireTime) {
                this.lastToastTime.delete(key);
            }
        }
    }

    /**
     * 错误日志
     */
    logError(method, error) {
        console.error(`[ToastHelper.${method}] Error:`, error.message);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            queueSize: this.toastQueue.length,
            trackedDevices: this.lastToastTime.size,
            isShowingToast: this.isShowingToast
        };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ToastHelper;
} else if (typeof global !== 'undefined') {
    global.ToastHelper = ToastHelper;
}
